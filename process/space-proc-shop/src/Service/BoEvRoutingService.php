<?php

namespace App\Service;

use Space\MongoDocuments\Service\BoEvRoutingService as BaseBoEvRoutingService;
use Space\MongoDocuments\Document\BoEvRouting;
use App\Trait\LoggerTrait;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class BoEvRoutingService
{
    use LoggerTrait;

    public function __construct(
        private BaseBoEvRoutingService $baseBoEvRoutingService
    ) {
    }

    /**
     * Find BO EV Routing by vehicle parameters
     */
    public function findByVehicleParameters(string $lcdv, string $dvq, string $dar, string $b0f, string $brand, string $country, string $language): ?BoEvRouting
    {
        try {
            return $this->baseBoEvRoutingService->findByVehicleParameters($lcdv, $dvq, $dar, $b0f, $brand, $country, $language);
        } catch (\Exception $e) {
            $this->logger->error('Error finding BO EV Routing by vehicle parameters', [
                'lcdv' => $lcdv,
                'dvq' => $dvq,
                'dar' => $dar,
                'b0f' => $b0f,
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find BO EV Routing by vehicle parameters', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find BO EV Routing by LCDV
     */
    public function findByLcdv(string $lcdv): array
    {
        try {
            return $this->baseBoEvRoutingService->findByLcdv($lcdv);
        } catch (\Exception $e) {
            $this->logger->error('Error finding BO EV Routing by LCDV', [
                'lcdv' => $lcdv,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find BO EV Routing by LCDV', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find enabled BO EV Routing configurations
     */
    public function findEnabled(): array
    {
        try {
            return $this->baseBoEvRoutingService->findEnabled();
        } catch (\Exception $e) {
            $this->logger->error('Error finding enabled BO EV Routing configurations', [
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find enabled BO EV Routing configurations', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find by brand and country
     */
    public function findByBrandAndCountry(string $brand, string $country): array
    {
        try {
            return $this->baseBoEvRoutingService->findByBrandAndCountry($brand, $country);
        } catch (\Exception $e) {
            $this->logger->error('Error finding BO EV Routing by brand and country', [
                'brand' => $brand,
                'country' => $country,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find BO EV Routing by brand and country', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Save BO EV Routing
     */
    public function save(BoEvRouting $boEvRouting): void
    {
        try {
            $this->baseBoEvRoutingService->save($boEvRouting);
        } catch (\Exception $e) {
            $this->logger->error('Error saving BO EV Routing', [
                'lcdv' => $boEvRouting->getLcdv(),
                'brand' => $boEvRouting->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save BO EV Routing', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Create new BO EV Routing
     */
    public function create(string $lcdv, string $dvq, string $dar, string $b0f, string $brand, string $country, string $language): BoEvRouting
    {
        try {
            return $this->baseBoEvRoutingService->create($lcdv, $dvq, $dar, $b0f, $brand, $country, $language);
        } catch (\Exception $e) {
            $this->logger->error('Error creating BO EV Routing', [
                'lcdv' => $lcdv,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to create BO EV Routing', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update charging curve array
     */
    public function updateChargingCurveArray(BoEvRouting $boEvRouting, array $chargingCurveArray): void
    {
        try {
            $this->baseBoEvRoutingService->updateChargingCurveArray($boEvRouting, $chargingCurveArray);
        } catch (\Exception $e) {
            $this->logger->error('Error updating charging curve array', [
                'lcdv' => $boEvRouting->getLcdv(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update charging curve array', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Get BO EV Routing data as array (for backward compatibility)
     */
    public function getAsArray(BoEvRouting $boEvRouting): array
    {
        try {
            return $this->baseBoEvRoutingService->getAsArray($boEvRouting);
        } catch (\Exception $e) {
            $this->logger->error('Error getting BO EV Routing as array', [
                'lcdv' => $boEvRouting->getLcdv(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get BO EV Routing as array', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find or create BO EV Routing
     */
    public function findOrCreate(string $lcdv, string $dvq, string $dar, string $b0f, string $brand, string $country, string $language): BoEvRouting
    {
        try {
            return $this->baseBoEvRoutingService->findOrCreate($lcdv, $dvq, $dar, $b0f, $brand, $country, $language);
        } catch (\Exception $e) {
            $this->logger->error('Error finding or creating BO EV Routing', [
                'lcdv' => $lcdv,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find or create BO EV Routing', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }
}
