<?php

namespace App\Service;

use Space\MongoDocuments\Service\DrivingScoreService as BaseDrivingScoreService;
use Space\MongoDocuments\Document\DrivingScore;
use App\Trait\LoggerTrait;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class DrivingScoreService
{
    use LoggerTrait;

    public function __construct(
        private BaseDrivingScoreService $baseDrivingScoreService
    ) {
    }

    /**
     * Find driving score by VIN
     */
    public function findByVin(string $vin): ?DrivingScore
    {
        try {
            return $this->baseDrivingScoreService->findByVin($vin);
        } catch (\Exception $e) {
            $this->logger->error('Error finding driving score by VIN', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find driving score', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find driving score by contract number
     */
    public function findByContractNumber(string $contractNumber): ?DrivingScore
    {
        try {
            return $this->baseDrivingScoreService->findByContractNumber($contractNumber);
        } catch (\Exception $e) {
            $this->logger->error('Error finding driving score by contract number', [
                'contractNumber' => $contractNumber,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find driving score by contract number', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find driving scores by STLI policy number
     */
    public function findByStliPolicyNumber(string $stliPolicyNumber): array
    {
        try {
            return $this->baseDrivingScoreService->findByStliPolicyNumber($stliPolicyNumber);
        } catch (\Exception $e) {
            $this->logger->error('Error finding driving scores by STLI policy number', [
                'stliPolicyNumber' => $stliPolicyNumber,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find driving scores by STLI policy number', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find valid driving scores
     */
    public function findValid(): array
    {
        try {
            return $this->baseDrivingScoreService->findValid();
        } catch (\Exception $e) {
            $this->logger->error('Error finding valid driving scores', [
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find valid driving scores', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Save driving score
     */
    public function save(DrivingScore $drivingScore): void
    {
        try {
            $this->baseDrivingScoreService->save($drivingScore);
        } catch (\Exception $e) {
            $this->logger->error('Error saving driving score', [
                'vin' => $drivingScore->getVin(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save driving score', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Create new driving score
     */
    public function create(string $vin): DrivingScore
    {
        try {
            return $this->baseDrivingScoreService->create($vin);
        } catch (\Exception $e) {
            $this->logger->error('Error creating driving score', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to create driving score', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update driving score data
     */
    public function updateScoreData(DrivingScore $drivingScore, array $overallScore = null, array $dailyScore = null): void
    {
        try {
            $this->baseDrivingScoreService->updateScoreData($drivingScore, $overallScore, $dailyScore);
        } catch (\Exception $e) {
            $this->logger->error('Error updating driving score data', [
                'vin' => $drivingScore->getVin(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update driving score data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Get driving score with enhanced data
     */
    public function getEnhancedScore(string $vin): ?array
    {
        try {
            return $this->baseDrivingScoreService->getEnhancedScore($vin);
        } catch (\Exception $e) {
            $this->logger->error('Error getting enhanced driving score', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get enhanced driving score', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update global score and component scores
     */
    public function updateGlobalScore(DrivingScore $drivingScore, int $globalScore, int $accelerationScore = null, int $brakingScore = null): void
    {
        try {
            $this->baseDrivingScoreService->updateGlobalScore($drivingScore, $globalScore, $accelerationScore, $brakingScore);
        } catch (\Exception $e) {
            $this->logger->error('Error updating global score', [
                'vin' => $drivingScore->getVin(),
                'globalScore' => $globalScore,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update global score', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }
}
