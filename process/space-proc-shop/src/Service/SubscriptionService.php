<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Connector\SysSamsDataConnector;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;
use Symfony\Component\HttpFoundation\Request;

class SubscriptionService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private SysSamsDataConnector $connector)
    {
    }

    /**
     * get Subscription data.
     */
    public function getSubscription(string $userId, string $vin, string $target): WSResponse
    {
        $options = [
            'query' => [
                'target' => $target
            ],
            'headers' => [
                'userId' => $userId,
                'vin' => $vin,
            ],
        ];
        $url = '/v1/subscription';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", $options);

        $response = $this->connector->call(Request::METHOD_GET, $url, $options);

        return $response;
    }
    
    /**
     * Checkout cart items.
     */
    public function cartItemsCheckout(
        string $userDbId, 
        string $userId, 
        string $vin, 
        string $brand, 
        string $country, 
        string $language, 
        string $source, 
        string $target,
        array $body
    ): WSResponse {
        $options = [
            'headers' => [
                'userId' => $userDbId,
                'vin' => $vin,
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'source' => $source,
                'target' => $target
            ],
            'json' => $body
        ];
        
        $url = '/v1/subscription/items/checkout';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", $options);

        $response = $this->connector->call(Request::METHOD_POST, $url, $options);

        return $response;
    }
}
