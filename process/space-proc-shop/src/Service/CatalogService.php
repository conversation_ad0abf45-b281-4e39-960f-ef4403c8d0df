<?php

namespace App\Service;

use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;

/**
 * Catalog service.
 */
class CatalogService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private SysSamsDataConnector $connector
    ) {
    }

    /**
     * get Catalog data.
     */
    public function getCatalog(array $params): WSResponse
    {
        $options = [
            'query' => [
                'brand' => $params['brand'],
                'country' => $params['country'],
                'language' => $params['language'],
            ],
            'headers' => [
                'userId' => $params['userDbId'],
                'vin' => $params['vin'],
            ],
        ];
        $url = '/v1/catalog';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", $options);

        $response = $this->connector->call(Request::METHOD_GET, $url, $options);

        return $response;
    }
}
