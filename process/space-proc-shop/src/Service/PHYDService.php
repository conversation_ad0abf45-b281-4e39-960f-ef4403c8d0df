<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Model\VehicleModel;
use Symfony\Component\Serializer\SerializerInterface;
use App\Service\DrivingScoreService;
use Symfony\Component\HttpFoundation\Response;


/**
 * PHYD service.
 */
class PHYDService
{
    use LoggerTrait;

    private VehicleModel $vehicleModel;

    public const COLLECTION = 'drivingScore';

    public const FEATURE_CODE_PHYD = 'UBI_PHYD';

    public const FEATURE_CODE_STATUS_ENABLE = 'enable';
    public const FEATURE_CODE_STATUS_DISABLE = 'disable';

    public function __construct(
        private DrivingScoreService $drivingScoreService,
        private SerializerInterface $serializer
    ) {
    }

    /**
     * PHYD - Getting Driving Score Data
     *
     * @param string $vin The Vehicle Identification Number (VIN) to filter by.
     * @param string|null $stliPolicyNumber (Optional) The STLI Policy Number to include in the filter.
     */
    public function getDrivingScore(string $vin, ?string $stliPolicyNumber = null): WSResponse
    {
        $this->logger->info('Getting driving score', [
            'vin' => $vin,
            'stliPolicyNumber' => $stliPolicyNumber,
        ]);

        try {
            $drivingScore = $this->drivingScoreService->findByVin($vin);

            if ($drivingScore && (!$stliPolicyNumber || $drivingScore->getStliPolicyNumber() === $stliPolicyNumber)) {
                // Use the enhanced score method to get array representation
                $scoreArray = $this->drivingScoreService->getEnhancedScore($vin);
                $data = [
                    'documents' => [$scoreArray]
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($data));
            } else {
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['documents' => []]));
            }
        } catch (\Exception $e) {
            $this->logger->error('Error getting driving score', [
                'vin' => $vin,
                'stliPolicyNumber' => $stliPolicyNumber,
                'error' => $e->getMessage()
            ]);
            return new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode(['error' => $e->getMessage()]));
        }
    }

    /**
     * Constructs a MongoDB filter for fetching driving score data based on VIN
     * and optionally stliPolicyNumber.
     *
     * @param string|null $vin              The Vehicle Identification Number (VIN) to filter by.
     * @param string|null $stliPolicyNumber (Optional) The STLI Policy Number to filter by.
     */
    public function getDrivingScoreFilter(?string $vin, ?string $stliPolicyNumber = null)
    {
        $filter = [
            [
                '$match' => ['vin' => $vin],
            ],
        ];

        if (!empty($stliPolicyNumber)) {
            $filter[0]['$match']['stliPolicyNumber'] = $stliPolicyNumber;
        }
        return $filter;
    }
}
