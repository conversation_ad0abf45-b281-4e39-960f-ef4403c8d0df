<?php

namespace App\Service;

use Space\MongoDocuments\Service\SettingsService as BaseSettingsService;
use Space\MongoDocuments\Document\Settings;
use App\Trait\LoggerTrait;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class SettingsService
{
    use LoggerTrait;

    public function __construct(
        private BaseSettingsService $baseSettingsService
    ) {
    }

    /**
     * Get O2X settings (for DealerManager)
     */
    public function getO2xSettings(string $brand, string $country, string $culture): ?array
    {
        try {
            return $this->baseSettingsService->getO2xSettings($brand, $country, $culture);
        } catch (\Exception $e) {
            $this->logger->error('Error getting O2X settings', [
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get O2X settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find settings by type, brand, country, and culture
     */
    public function findByTypeAndBrandAndCountryAndCulture(string $type, string $brand, string $country, string $culture): ?Settings
    {
        try {
            return $this->baseSettingsService->findByTypeAndBrandAndCountryAndCulture($type, $brand, $country, $culture);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings', [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find settings by type and brand
     */
    public function findByTypeAndBrand(string $type, string $brand): array
    {
        try {
            return $this->baseSettingsService->findByTypeAndBrand($type, $brand);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by type and brand', [
                'type' => $type,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings by type and brand', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find settings by type
     */
    public function findByType(string $type): array
    {
        try {
            return $this->baseSettingsService->findByType($type);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by type', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings by type', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Save settings
     */
    public function save(Settings $settings): void
    {
        try {
            $this->baseSettingsService->save($settings);
        } catch (\Exception $e) {
            $this->logger->error('Error saving settings', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Create new settings
     */
    public function create(string $type, string $brand, string $country, string $culture): Settings
    {
        try {
            return $this->baseSettingsService->create($type, $brand, $country, $culture);
        } catch (\Exception $e) {
            $this->logger->error('Error creating settings', [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to create settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update settings data
     */
    public function updateData(Settings $settings, array $data): void
    {
        try {
            $this->baseSettingsService->updateData($settings, $data);
        } catch (\Exception $e) {
            $this->logger->error('Error updating settings data', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update settings data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update settings data by key-value
     */
    public function updateSettingsData(Settings $settings, array $settingsData): void
    {
        try {
            $this->baseSettingsService->updateSettingsData($settings, $settingsData);
        } catch (\Exception $e) {
            $this->logger->error('Error updating settings data', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update settings data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }
}
