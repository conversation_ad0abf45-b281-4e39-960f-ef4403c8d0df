<?php

namespace App\Tests\Service;

use App\Service\EvRoutingService;
use App\Service\UserDataService;
use App\Service\BoEvRoutingService;
use App\Connector\CustomHttpClient;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Space\MongoDocuments\Document\UserData;

class EvRoutingServiceTest extends TestCase
{
    private EvRoutingService $evRoutingService;
    private UserDataService $userDataService;
    private BoEvRoutingService $boEvRoutingService;
    private CustomHttpClient $customHttpClient;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->userDataService = $this->createMock(UserDataService::class);
        $this->boEvRoutingService = $this->createMock(BoEvRoutingService::class);
        $this->customHttpClient = $this->createMock(CustomHttpClient::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->evRoutingService = new EvRoutingService(
            $this->userDataService,
            $this->boEvRoutingService,
            $this->customHttpClient,
            'https://example-bo-api.com'
        );
        $this->evRoutingService->setLogger($this->logger);
    }
    
    /**
     * Test the getLcdvByVin method with successful response
     */
    public function testGetLcdvByVinSuccess(): void
    {
        // Test data
        $vin = 'VIN123456789';
        $expectedLcdv = 'LCDV123';

        // Configure UserDataService mock
        $this->userDataService->expects($this->once())
            ->method('getLcdvByVin')
            ->with($vin)
            ->willReturn($expectedLcdv);

        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);

        // Assertions
        $this->assertEquals($expectedLcdv, $result);
    }
    
    /**
     * Test the getLcdvByVin method with empty response
     */
    public function testGetLcdvByVinEmptyResponse(): void
    {
        // Test data
        $vin = 'VIN123456789';

        // Configure UserDataService mock to return null
        $this->userDataService->expects($this->once())
            ->method('getLcdvByVin')
            ->with($vin)
            ->willReturn(null);

        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);

        // Assertions
        $this->assertEquals('', $result);
    }
    
    /**
     * Test the getLcdvByVin method with error response
     */
    public function testGetLcdvByVinError(): void
    {
        // Test data
        $vin = 'VIN123456789';
        $errorCode = Response::HTTP_INTERNAL_SERVER_ERROR;
        $errorMessage = 'Database error';

        // Configure UserDataService mock to throw exception
        $this->userDataService->expects($this->once())
            ->method('getLcdvByVin')
            ->with($vin)
            ->willThrowException(new \Exception($errorMessage, $errorCode));

        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);

        // Assertions - should return WSResponse on error
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals($errorCode, $result->getCode());
    }

    /**
     * Test the getLcdvByVin method with non-200 response
     */
    public function testGetLcdvByVinNon200Response(): void
    {
        // Test data
        $vin = 'VIN123456789';

        // Configure UserDataService mock to return null (simulating not found)
        $this->userDataService->expects($this->once())
            ->method('getLcdvByVin')
            ->with($vin)
            ->willReturn(null);

        // Call the method
        $result = $this->evRoutingService->getLcdvByVin($vin);

        // Assertions - should return empty string for non-200 response
        $this->assertEquals('', $result);
    }
}
