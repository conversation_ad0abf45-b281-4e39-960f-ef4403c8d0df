<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Service\PHYDService;
use App\Service\DrivingScoreService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Space\MongoDocuments\Document\DrivingScore;

class PHYDServiceTest extends TestCase
{
    private DrivingScoreService $drivingScoreService;
    private LoggerInterface $logger;
    private PHYDService $PHYDService;
    private SerializerInterface $serializer;

    public function setUp(): void
    {
        $this->drivingScoreService = $this->createMock(DrivingScoreService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->PHYDService = new PHYDService($this->drivingScoreService, $this->serializer);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->PHYDService->setLogger($this->logger);
    }

    public function testGetDrivingScore(): void
    {
        $vin = 'VR3UPHNKSKT101603';
        $stliPolicyNumber = 'CN123321';

        // Create mock driving score document
        $drivingScore = $this->createMock(DrivingScore::class);
        $drivingScore->method('getStliPolicyNumber')->willReturn($stliPolicyNumber);

        // Mock enhanced score array
        $enhancedScore = [
            'vin' => $vin,
            'stliPolicyNumber' => $stliPolicyNumber,
            'globalScore' => 85,
            'overallScore' => ['value' => 40.15],
            'dailyScore' => ['value' => 38.5]
        ];

        $this->drivingScoreService
            ->expects($this->exactly(2))
            ->method('findByVin')
            ->with($vin)
            ->willReturn($drivingScore);

        $this->drivingScoreService
            ->expects($this->exactly(2))
            ->method('getEnhancedScore')
            ->with($vin)
            ->willReturn($enhancedScore);

        // Case without stliPolicyNumber
        $resultWithoutPolicy = $this->PHYDService->getDrivingScore($vin);
        $this->assertInstanceOf(WSResponse::class, $resultWithoutPolicy);
        $this->assertEquals(Response::HTTP_OK, $resultWithoutPolicy->getCode());

        // Case with stliPolicyNumber
        $resultWithPolicy = $this->PHYDService->getDrivingScore($vin, $stliPolicyNumber);
        $this->assertInstanceOf(WSResponse::class, $resultWithPolicy);
        $this->assertEquals(Response::HTTP_OK, $resultWithPolicy->getCode());
    }


    public function testGetDrivingScoreNotFound(): void
    {
        $vin = 'VR3UPHNKSKT101603';

        $this->drivingScoreService
            ->expects($this->once())
            ->method('findByVin')
            ->with($vin)
            ->willReturn(null);

        $result = $this->PHYDService->getDrivingScore($vin);
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
    }
}