<?php

namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';

use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Psr\Log\LoggerInterface;

use App\Service\SubscriptionService;
use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;

class SubscriptionServiceTest extends TestCase
{
    private $subscriptionService;
    private $connector;
    private $logger;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SysSamsDataConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->subscriptionService = new SubscriptionService($this->connector);
        $this->subscriptionService->setLogger($this->logger);
    }

    public function getSubscriptionOutput()
    {
        $data = [
            'success' =>
            [
                "reference" => "A-S00407030",
                "title" => "Pack Navigation connectée",
                "description" => "<h4>LA NOUVELLE GÉNÉRATION DE NAVIGATION CONNECTÉE&nbsp;</h4>",
                "shop" => "SAMS",
                "subscriberId" => "DS-ACNT200000275706",
                "systemType" => "c@",
                "vehicleId" => "VR1URHNSSKW013718",
                "type" => "Commercial Service",
                "status" => "Pending identification",
                "statusReason" => "ERROR",
                "brand" => "DS",
                "country" => "FR",
                "culture" => "fr-FR",
                "startDate" => "2024-11-29T08:12:19.112Z",
                "endDate" => "cvcfgdgs",
                "associationId" => "92076a17-1192-4dee-9f11-9b000dba0a6e",
                "associationLevel" => "LOW",
                "isExtensible" => false
            ]
        ];
        return $data;
    }

    public function testGetSubscription()
    {
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(200, $this->getSubscriptionOutput()));
        $response = $this->subscriptionService->getSubscription('ACNT200000328091', 'VR1URHNSSKW013718', 'B2C');
        $this->assertEquals(200, $response->getCode());
    }

    public function testGetSubscriptionWithInvalidVin()
    {
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(404, ["error" => "Not Found"]));
        $response = $this->subscriptionService->getSubscription('ACNT200000328091', 'VR1URHNSSKW013788', 'B2C');
        $this->assertEquals(404, $response->getCode());
    }


}
