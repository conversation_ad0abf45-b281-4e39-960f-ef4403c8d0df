<?php

namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';

use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Psr\Log\LoggerInterface;

use App\Service\CatalogService;
use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;

class CatalogServiceTest extends TestCase
{
    private $catalogService;
    private $connector;
    private $logger;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(SysSamsDataConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->catalogService = new CatalogService($this->connector);
        $this->catalogService->setLogger($this->logger);
    }

    public function getCatalogOutput()
    {
        $data = [
            'success' =>
            [
                "id" => "8adceeb38fbea01f018fc4bf967b0234",
                "type" => "Bundle",
                "title" => "Connect ONE",
                "topMainImage" => "",
                "category" => "OTHERS",
                "description" => "<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);\"><span style=\"-webkit-text-stroke-width:0px;display:inline !important;float:none;font-family:&quot;Aptos Narrow&quot;;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;\">Godeti la tranquillità con le nostre funzioni di sicurezza e assistenza per la tua </span></span><span style=\"color:hsl(0,0%,46%);\">[sams-token:active_brand_name]</span><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);\"><span style=\"-webkit-text-stroke-width:0px;display:inline !important;float:none;font-family:&quot;Aptos Narrow&quot;;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;\">, senza costi aggiuntivi.</span></span></p>",
                "productGroupName" => "CONNECTONE",
                "urlSso" => "https://ppr.services-store.alfaromeo.com/en/login-redirect?redirect-url=https://ppr.services-store.alfaromeo.com/en/node/128",
                "familyName" => "CONNECTONE",
                "status" => "Activated",
                "offers" => [
                    [
                        "pricingModel" => "One Off",
                        "prices" => [
                            [
                                "currency" => "EUR",
                                "price" => 0,
                                "periodType" => null
                            ]
                        ]
                    ]
                ]
            ]
        ];
        return $data;
    }

    public function testGetCatalog()
    {
        $params = [
            'brand' => 'AL',
            'country' => 'IT',
            'language' => 'it',
            'userId' => 'ACNT200000328091',
            'userDbId' => 'ACNT200000328091',
            'vin' => 'VR1URHNSSKW013718',
        ];
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(200, $this->getCatalogOutput()));
        $response = $this->catalogService->getCatalog($params);
        $this->assertEquals(200, $response->getCode());
    }

    public function testGetCatalogWithInvalidVin()
    {
        $params = [
            'brand' => 'AL',
            'country' => 'IT',
            'language' => 'it',
            'userId' => 'ACNT200000328091',
            'userDbId' => 'ACNT200000328091',
            'vin' => 'VR1URHNSSKW013788',
        ];
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(404, ["error" => "Not Found"]));
        $response = $this->catalogService->getCatalog($params);
        $this->assertEquals(404, $response->getCode());
    }
    
}
