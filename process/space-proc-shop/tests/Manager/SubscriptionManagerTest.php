<?php

namespace Tests\Manager;

require __DIR__ . '/../../vendor/autoload.php';

use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use App\Manager\SubscriptionManager;
use App\Service\ContribService;
use App\Connector\SysSamsDataConnector;
use App\DataMapper\SubscriptionDataMapper;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\UserManager;

class SubscriptionManagerTest extends TestCase
{
    private $subscriptionManager;
    private $contribService;
    private $sysSamsDataConnector;
    private $subscriptionDataMapper;
    private $logger;
    private $validator;
    private $userManager;
    protected function setUp(): void
    {
        $this->contribService = $this->createMock(ContribService::class);
        $this->subscriptionService = $this->createMock(SubscriptionService::class);
        $this->subscriptionDataMapper = $this->createMock(SubscriptionDataMapper::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->subscriptionManager = new SubscriptionManager($this->subscriptionService, $this->contribService, $this->validator, $this->subscriptionDataMapper, $this->userManager);
        $this->subscriptionManager->setLogger($this->logger);
    }

    public function subscriptionOutput(): array
    {
        $data = [
            'success' => [
                'vehicleProvisionings' => [
                    [
                        "vehOrderId" => null,
                        "vehicleProvisioningId" => "a1tVe000006MXQ5IAO",
                        "vehicle" => "VR1URHNSSKW013718",
                        "transactions" => [
                            "vin" => null,
                            "transactionVehicleOrderId" => null,
                            "transactionTermStartDate" => null,
                            "transactionStatusReason" => null,
                            "transactionStatus" => null,
                            "transactionServiceActivationDate" => null,
                            "transactionName" => null,
                            "transactionInitialTerm" => null,
                            "transactionId" => null,
                            "transactionEShopOrderId" => null,
                            "transactionContractEffectiveDate" => null,
                            "transactionChannel" => null,
                            "transactionCancelRequestDate" => null,
                            "transactionCancelReason" => null,
                            "transactionActivationStatus" => null,
                            "subscriptionCountry" => null,
                            "orderId" => null,
                            "hasSubscriptionProductAndCharge" => null,
                            "cultureCode" => null,
                            "billingAccountId" => null,
                            "assetId" => null,
                            "accountId" => null
                        ],
                        "subscription" => [
                            "subscriptionName" => "A-S00407030",
                            "subscriptionId" => "a0HVe000000qX7xMAE",
                            "status" => "Pending identification",
                            "rrdi" => null,
                            "restitutionId" => null,
                            "ratePlans" => [
                                [
                                    "translatedDescription" => "Pack Navigation connectée",
                                    "target" => "B2C",
                                    "status" => "Active",
                                    "ratePlanName" => "[TEST PURPOSES] NAVCO8/ZAR PSA - [TEST] 1 year paid ONE OFF",
                                    "ratePlanId" => "ceb1bccf34c93491e6c376f9e6bfa5b2",
                                    "ratePlanCharges" => [
                                        [
                                            "type" => "One-Time",
                                            "ratePlanCurrency" => "EUR",
                                            "rateplanChargeTiers" => [
                                                [
                                                    "tier" => "1",
                                                    "ratePlanChargeCurrency" => "EUR",
                                                    "price" => "109.00",
                                                    "id" => "a0FVe0000024G3RMAU"
                                                ]
                                            ],
                                            "period" => null,
                                            "name" => "[TEST] 1 year paid",
                                            "model" => "Flat Fee Pricing",
                                            "listPrice" => "Per Billing Period",
                                            "id" => "a0DVe000001fK3pMAE",
                                            "bundleLevel" => null,
                                            "bundleGroup" => null
                                        ]
                                    ],
                                    "productRatePlanId" => "8adceeb38e3b8d14018e3c50d91c3fb8",
                                    "product" => [
                                        "vehProvProductRelations" => [],
                                        "type" => "Commercial Service",
                                        "sku" => "SKU-00000088-DS",
                                        "productStatus" => "Activated",
                                        "productName" => "NAVCO8/ZAR PSA",
                                        "productFeatures" => [
                                            [
                                                "featureName" => "Reconnaissance Vocale débarquée",
                                                "featureStatus" => null,
                                                "featureCode" => null,
                                                "servicesIds" => null,
                                                "type" => "FDS"
                                            ],
                                            [
                                                "featureName" => "Affichage bornes de recharge",
                                                "featureStatus" => null,
                                                "featureCode" => null,
                                                "servicesIds" => null,
                                                "type" => "FDS"
                                            ],
                                            [
                                                "featureName" => "Navigation connectée",
                                                "featureStatus" => null,
                                                "featureCode" => null,
                                                "servicesIds" => null,
                                                "type" => "FDS"
                                            ],
                                            [
                                                "featureName" => "Zone à risque en connexion",
                                                "featureStatus" => null,
                                                "featureCode" => null,
                                                "servicesIds" => null,
                                                "type" => "FDS"
                                            ]
                                        ],
                                        "productFamily" => "NAVCOZAR",
                                        "productCommercialName" => "Pack Navigation connectée",
                                        "productCode" => "8adcd9eb6f36eba7016f6b7a96001af0",
                                        "marketingInfoProductId" => "8adcd9eb6f36eba7016f6b7a96001af0",
                                        "groupName" => "NAVCOZAR",
                                        "category" => "Base Products",
                                        "brand" => "DS",
                                        "activationChannel" => "PROMAN"
                                    ],
                                    "pricingModel" => "One Off",
                                    "leads" => null,
                                    "duration" => "12",
                                    "country" => "FR",
                                    "category" => "Subscribable",
                                    "brand" => "DS"
                                ]
                            ],
                            "isRenewable" => false,
                            "isExtensible" => false,
                            "hasFreeTrial" => "False",
                            "cultureCode" => "fr-FR",
                            "clientReset" => false,
                            "b2bContractNumber" => null,
                            "b2bContractName" => null,
                            "b2bContractId" => null,
                            "autoRenew" => false,
                            "acntNum" => "A00520091"
                        ],
                        "subscriberAccount" => [
                            "target" => "PersonAccount",
                            "systemType" => "c@",
                            "street" => "99 boulevard de Prague",
                            "region" => null,
                            "postalCode" => "93160",
                            "legalEntityName" => null,
                            "externalId" => "DS-ACNT200000275706",
                            "customerPhoneNumber" => null,
                            "customerLastName" => "SAMS",
                            "customerLanguage" => "fr-FR",
                            "customerIdSams" => "0015E00003ChxkEQAR",
                            "customerFirstName" => "Hanumanth K",
                            "customerEmail" => "<EMAIL>",
                            "country" => "FR",
                            "civility" => "Mr.",
                            "city" => "Noisy-le-grand"
                        ],
                        "statusReason" => "ERROR",
                        "startDate" => "2024-11-29T08:12:19.112Z",
                        "provisionRequestId" => "674977572b623c6b608bb95d",
                        "pmBundleId" => null,
                        "pcsContractId" => null,
                        "modelType" => "Standard",
                        "lastModificationDate" => "2024-12-13T08:12:52.392Z",
                        "invoiceOwner" => [
                            "target" => null,
                            "systemType" => null,
                            "street" => null,
                            "region" => null,
                            "postalCode" => null,
                            "legalEntityName" => null,
                            "externalId" => null,
                            "customerPhoneNumber" => null,
                            "customerLastName" => null,
                            "customerLanguage" => null,
                            "customerIdSams" => null,
                            "customerFirstName" => null,
                            "customerEmail" => null,
                            "country" => null,
                            "civility" => null,
                            "city" => null
                        ],
                        "endDate" => null,
                        "deactivationDate" => null,
                        "countryCode" => "FR",
                        "channel" => "SAMS_Drupal",
                        "brand" => "DS",
                        "associationLevel" => "LOW",
                        "associationJourney" => null,
                        "associationId" => "92076a17-1192-4dee-9f11-9b000dba0a6e",
                        "activationStatus" => "Pending activation",
                        "activationDate" => null,
                        "amRequestId" => "581731e9-4dd0-4abc-86c5-bb9b2cb9ff7d"
                    ]
                ]
            ]
        ];
        return $data;
    }

    public function subscriptionModelObject()
    {
        $data = [
            "reference" => "A-S00407030",
            "title" => "Pack Navigation connectée",
            "description" => "<h4>LA NOUVELLE GÉNÉRATION DE NAVIGATION CONNECTÉE&nbsp;</h4>",
            "shop" => "SAMS",
            "subscriberId" => "DS-ACNT200000275706",
            "systemType" => "c@",
            "vehicleId" => "VR1URHNSSKW013718",
            "type" => "Commercial Service",
            "status" => "Pending identification",
            "statusReason" => "ERROR",
            "brand" => "DS",
            "country" => "FR",
            "culture" => "fr-FR",
            "startDate" => "2024-11-29T08:12:19.112Z",
            "endDate" => "cvcfgdgs",
            "associationId" => "92076a17-1192-4dee-9f11-9b000dba0a6e",
            "associationLevel" => "LOW",
            "isExtensible" => false
        ];
        return $data;
    }

    public function contribOutput()
    {
        $data = [
            'success' => [
                "homePage" => "https://ppr.services-store.dsautomobiles.co.uk/",
                "homePageSso" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?redirect-url=https://ppr.services-store.dsautomobiles.co.uk/",
                "title" => "Telemaintenance",
                "fullDescription" => "<h4>LET DS&nbsp;LOOK AFTER YOUR SERVICE AND MAINTENANCE REMINDERS FOR YOU</h4>\n\n<p>You’ve got enough to think about, without having to remember when your vehicle needs servicing. &nbsp;</p>\n\n<p>With our intelligent Telemaintenance&nbsp;, DS&nbsp;continuously monitors the key functions of your vehicle and can let you know as soon as there is something that you need to get fixed.&nbsp;</p>\n\n<p>Telemaintenance&nbsp;is provided free of charge by DS&nbsp;for a minimum of 3 years, or for as long you own the vehicle and remain subscribed. &nbsp;Full information can be found in the<a data-entity-type=\"file\" data-entity-uuid=\"dabfd290-ed7f-4663-89f0-a217c1c45d62\" href=\"/sites/ds/files/uploaded-files/2102%20-%20DS%20Telemaintenance%20TsCs.pdf\" title=\" DS Telemaintenance TsCs.pdf \">&nbsp;</a><a data-entity-type=\"file\" data-entity-uuid=\"5ebfaca2-45dc-4503-a882-d0462a1233d9\" href=\"/sites/ds/files/uploaded-files/TC_Connect_One_UK.pdf\" title=\" DS Telemaintenance TsCs.pdf \"><u><strong>Telemaintenance&nbsp;Ts&amp;Cs</strong></u></a><a data-entity-type=\"file\" data-entity-uuid=\"dabfd290-ed7f-4663-89f0-a217c1c45d62\" href=\"/sites/ds/files/uploaded-files/2102%20-%20DS%20Telemaintenance%20TsCs.pdf\" title=\" DS Telemaintenance TsCs.pdf \">.&nbsp;</a></p>\n",
                "shortDescription" => "With Telemaintenance, DS lets you know if your vehicle requires scheduled maintenance or some extra attention.",
                "productUrl" => "https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
                "productUrlSso" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?redirect-url=https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
                "productUrlCvs" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?xcsrf=[VIN]&jwt=[TOKEN_CVS]&inboundApplication=[Mymark]&redirect-url=https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
                "marketingProductSheetId" => "35",
                "productId" => "8adc8f99680e4ebd016819a43e56517b",
                "features" => [
                    [
                        "desc" => "<p>Real time maintenance alerts to look after your vehicle and to always know the next service needed</p>\r\n",
                        "icon" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TMTS%20%28big%29.png",
                        "full_desc" => "<p><strong>What you need to know, when you need to know</strong><br />\r\nDS intelligent maintenance features are your car’s personal assistant: the vehicle's key functions are constantly monitored and you are alerted if anything needs your attention.&nbsp;<br />\r\nWe also remind of your next service, making it easy to book an appointment in seconds.</p>\r\n"
                    ]
                ],
                "topMainImage" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/styles/service_slider_element_desktop/public/2024-12/5-Telemaintenance-350x244.jpg",
                "tnc" => [
                    "version" => "3.2",
                    "title" => "T&C TM/TS",
                    "id" => "36",
                    "tncUrl" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TC_Connect_One_UK.pdf"
                ],
                "legals" => [],
                "consents" => [
                    [
                        "category" => null,
                        "title" => "Geolocation Consents",
                        "shortDescription" => "I understand and accept that the provision of this(theses) service(s) requires vehicle geolocation and data will only be proceeded in accordance with the privacy policy.",
                        "longConsentDesc" => null,
                        "tnc" => [
                            "version" => "3.2",
                            "title" => "T&C TM/TS",
                            "id" => "36",
                            "tncUrl" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TC_Connect_One_UK.pdf"
                        ],
                        "data" => null,
                        "purpose" => null,
                        "consumer" => null
                    ]
                ]
            ]
        ];
        return $data;
    }

    public function testgetSubcsciption()
    {
        $this->userManager->method('getUserByUserId')->willReturn(new WSResponse(200, json_encode(['documents' => ['userId' => 'ACNT200000328062']])));
        $this->subscriptionService->method('getSubscription')->willReturn(new WSResponse(200, $this->subscriptionOutput()));
        $this->contribService->method('getContrib')->willReturn(new WSResponse(200, $this->contribOutput()));
        $this->subscriptionDataMapper->method('createList')->willReturn($this->subscriptionModelObject());
        $response = $this->subscriptionManager->getSubscription('ACNT200000328062', 'VR1URHNSSKW013718', 'B2C');
        $this->assertInstanceOf(SuccessResponse::class, $response);
    }

    public function testgetSubscriptionWithInvalidData()
    {
        $this->userManager->method('getUserByUserId')->willReturn(new WSResponse(200, json_encode(['documents' => ['userId' => 'ACNT200000328062']])));
        $this->subscriptionService->method('getSubscription')->willReturn(new WSResponse(200, ['success' => []]));
        $this->contribService->method('getContrib')->willReturn(new WSResponse(200, $this->contribOutput()));
        $this->subscriptionDataMapper->method('createList')->willReturn([]);
        $response = $this->subscriptionManager->getSubscription('ACNT200000328062', 'VR1URHNSSKW013718', 'B2C');
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }
}
