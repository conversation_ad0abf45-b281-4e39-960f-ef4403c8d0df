<?php

namespace Space\MongoDocuments\Service;

use Space\MongoDocuments\Document\BoEvRouting;
use Space\MongoDocuments\Repository\BoEvRoutingRepository;
use Psr\Log\LoggerInterface;
use RuntimeException;

class BoEvRoutingService
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Find BO EV Routing by vehicle parameters
     */
    public function findByVehicleParameters(string $lcdv, string $dvq, string $dar, string $b0f, string $brand, string $country, string $language): ?BoEvRouting
    {
        try {
            return $this->mongoDBService->findOneBy(BoEvRouting::class, [
                'lcdv' => $lcdv,
                'dvq' => $dvq,
                'dar' => $dar,
                'b0f' => $b0f,
                'brand' => $brand,
                'country' => $country,
                'language' => $language
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding BO EV Routing by vehicle parameters', [
                'lcdv' => $lcdv,
                'dvq' => $dvq,
                'dar' => $dar,
                'b0f' => $b0f,
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find BO EV Routing by vehicle parameters', 503, $e);
        }
    }

    /**
     * Find BO EV Routing by LCDV
     */
    public function findByLcdv(string $lcdv): array
    {
        try {
            return $this->mongoDBService->find(BoEvRouting::class, ['lcdv' => $lcdv]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding BO EV Routing by LCDV', [
                'lcdv' => $lcdv,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find BO EV Routing by LCDV', 503, $e);
        }
    }

    /**
     * Find enabled BO EV Routing configurations
     */
    public function findEnabled(): array
    {
        try {
            return $this->mongoDBService->find(BoEvRouting::class, ['enabled' => true]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding enabled BO EV Routing configurations', [
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find enabled BO EV Routing configurations', 503, $e);
        }
    }

    /**
     * Find by brand and country
     */
    public function findByBrandAndCountry(string $brand, string $country): array
    {
        try {
            return $this->mongoDBService->find(BoEvRouting::class, [
                'brand' => $brand,
                'country' => $country
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding BO EV Routing by brand and country', [
                'brand' => $brand,
                'country' => $country,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find BO EV Routing by brand and country', 503, $e);
        }
    }

    /**
     * Save BO EV Routing
     */
    public function save(BoEvRouting $boEvRouting): void
    {
        try {
            $this->mongoDBService->save($boEvRouting);
        } catch (\Exception $e) {
            $this->logger->error('Error saving BO EV Routing', [
                'lcdv' => $boEvRouting->getLcdv(),
                'brand' => $boEvRouting->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save BO EV Routing', 503, $e);
        }
    }

    /**
     * Create new BO EV Routing
     */
    public function create(string $lcdv, string $dvq, string $dar, string $b0f, string $brand, string $country, string $language): BoEvRouting
    {
        $boEvRouting = new BoEvRouting();
        $boEvRouting->setLcdv($lcdv);
        $boEvRouting->setDvq($dvq);
        $boEvRouting->setDar($dar);
        $boEvRouting->setB0f($b0f);
        $boEvRouting->setBrand($brand);
        $boEvRouting->setCountry($country);
        $boEvRouting->setLanguage($language);
        return $boEvRouting;
    }

    /**
     * Update charging curve array
     */
    public function updateChargingCurveArray(BoEvRouting $boEvRouting, array $chargingCurveArray): void
    {
        try {
            $boEvRouting->setChargingCurveArray($chargingCurveArray);
            $this->save($boEvRouting);
        } catch (\Exception $e) {
            $this->logger->error('Error updating charging curve array', [
                'lcdv' => $boEvRouting->getLcdv(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update charging curve array', 503, $e);
        }
    }

    /**
     * Get BO EV Routing data as array (for backward compatibility)
     */
    public function getAsArray(BoEvRouting $boEvRouting): array
    {
        return [
            'enabled' => $boEvRouting->getEnabled(),
            'label' => $boEvRouting->getLabel(),
            'constantSpeedConsumptionInkWhPerHundredkm' => $boEvRouting->getConstantSpeedConsumptionInkWhPerHundredkm(),
            'engineType' => $boEvRouting->getEngineType(),
            'maxChargeInkWh' => $boEvRouting->getMaxChargeInkWh(),
            'vehicleMaxSpeed' => $boEvRouting->getVehicleMaxSpeed(),
            'vehicleWeight' => $boEvRouting->getVehicleWeight(),
            'vehicleAxleWeight' => $boEvRouting->getVehicleAxleWeight(),
            'vehicleLength' => $boEvRouting->getVehicleLength(),
            'vehicleWidth' => $boEvRouting->getVehicleWidth(),
            'vehicleHeight' => $boEvRouting->getVehicleHeight(),
            'accelerationEfficiency' => $boEvRouting->getAccelerationEfficiency(),
            'decelerationEfficiency' => $boEvRouting->getDecelerationEfficiency(),
            'uphillEfficiency' => $boEvRouting->getUphillEfficiency(),
            'downhillEfficiency' => $boEvRouting->getDownhillEfficiency(),
            'chargingCurveArray' => $boEvRouting->getChargingCurveArray()
        ];
    }

    /**
     * Find or create BO EV Routing
     */
    public function findOrCreate(string $lcdv, string $dvq, string $dar, string $b0f, string $brand, string $country, string $language): BoEvRouting
    {
        $boEvRouting = $this->findByVehicleParameters($lcdv, $dvq, $dar, $b0f, $brand, $country, $language);
        if (!$boEvRouting) {
            $boEvRouting = $this->create($lcdv, $dvq, $dar, $b0f, $brand, $country, $language);
        }
        return $boEvRouting;
    }
}
