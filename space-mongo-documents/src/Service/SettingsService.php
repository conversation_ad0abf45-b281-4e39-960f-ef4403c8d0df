<?php

namespace Space\MongoDocuments\Service;

use Space\MongoDocuments\Document\Settings;
use Space\MongoDocuments\Repository\SettingsRepository;
use Psr\Log\LoggerInterface;
use RuntimeException;

class SettingsService
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Find settings by type, brand, country, and culture
     */
    public function findByTypeAndBrandAndCountryAndCulture(string $type, string $brand, string $country, string $culture): ?Settings
    {
        try {
            return $this->mongoDBService->findOneBy(Settings::class, [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings', [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings', 503, $e);
        }
    }

    /**
     * Find settings by type and brand
     */
    public function findByTypeAndBrand(string $type, string $brand): array
    {
        try {
            return $this->mongoDBService->find(Settings::class, [
                'type' => $type,
                'brand' => $brand
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by type and brand', [
                'type' => $type,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings by type and brand', 503, $e);
        }
    }

    /**
     * Find settings by type
     */
    public function findByType(string $type): array
    {
        try {
            return $this->mongoDBService->find(Settings::class, ['type' => $type]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by type', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings by type', 503, $e);
        }
    }

    /**
     * Save settings
     */
    public function save(Settings $settings): void
    {
        try {
            $this->mongoDBService->save($settings);
        } catch (\Exception $e) {
            $this->logger->error('Error saving settings', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save settings', 503, $e);
        }
    }

    /**
     * Create new settings
     */
    public function create(string $type, string $brand, string $country, string $culture): Settings
    {
        $settings = new Settings();
        $settings->setType($type);
        $settings->setBrand($brand);
        $settings->setCountry($country);
        $settings->setCulture($culture);
        return $settings;
    }

    /**
     * Update settings data
     */
    public function updateData(Settings $settings, array $data): void
    {
        try {
            $settings->setData($data);
            $this->save($settings);
        } catch (\Exception $e) {
            $this->logger->error('Error updating settings data', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update settings data', 503, $e);
        }
    }

    /**
     * Update settings data by key-value
     */
    public function updateSettingsData(Settings $settings, array $settingsData): void
    {
        try {
            $settings->setSettingsData($settingsData);
            $this->save($settings);
        } catch (\Exception $e) {
            $this->logger->error('Error updating settings data', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update settings data', 503, $e);
        }
    }

    /**
     * Get O2X settings (specific method for DealerManager)
     */
    public function getO2xSettings(string $brand, string $country, string $culture): ?array
    {
        try {
            $settings = $this->findByTypeAndBrandAndCountryAndCulture('o2x', $brand, $country, $culture);
            if (!$settings) {
                return null;
            }

            $data = $settings->getData();
            $settingsData = $settings->getSettingsData();

            // Merge data and settingsData for backward compatibility
            return array_merge($data, $settingsData);
        } catch (\Exception $e) {
            $this->logger->error('Error getting O2X settings', [
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get O2X settings', 503, $e);
        }
    }
}
