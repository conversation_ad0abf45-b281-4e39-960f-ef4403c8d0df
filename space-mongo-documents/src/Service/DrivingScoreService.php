<?php

namespace Space\MongoDocuments\Service;

use Space\MongoDocuments\Document\DrivingScore;
use Space\MongoDocuments\Repository\DrivingScoreRepository;
use Psr\Log\LoggerInterface;
use RuntimeException;

class DrivingScoreService
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Find driving score by VIN
     */
    public function findByVin(string $vin): ?DrivingScore
    {
        try {
            return $this->mongoDBService->findOneBy(DrivingScore::class, ['vin' => $vin]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding driving score by VIN', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find driving score', 503, $e);
        }
    }

    /**
     * Find driving score by contract number
     */
    public function findByContractNumber(string $contractNumber): ?DrivingScore
    {
        try {
            return $this->mongoDBService->findOneBy(DrivingScore::class, ['contractNumber' => $contractNumber]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding driving score by contract number', [
                'contractNumber' => $contractNumber,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find driving score by contract number', 503, $e);
        }
    }

    /**
     * Find driving scores by STLI policy number
     */
    public function findByStliPolicyNumber(string $stliPolicyNumber): array
    {
        try {
            return $this->mongoDBService->find(DrivingScore::class, ['stliPolicyNumber' => $stliPolicyNumber]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding driving scores by STLI policy number', [
                'stliPolicyNumber' => $stliPolicyNumber,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find driving scores by STLI policy number', 503, $e);
        }
    }

    /**
     * Find valid driving scores
     */
    public function findValid(): array
    {
        try {
            return $this->mongoDBService->find(DrivingScore::class, ['isValid' => true]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding valid driving scores', [
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find valid driving scores', 503, $e);
        }
    }

    /**
     * Save driving score
     */
    public function save(DrivingScore $drivingScore): void
    {
        try {
            // Sync embedded documents before saving
            $drivingScore->prePersist();
            $this->mongoDBService->save($drivingScore);
        } catch (\Exception $e) {
            $this->logger->error('Error saving driving score', [
                'vin' => $drivingScore->getVin(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save driving score', 503, $e);
        }
    }

    /**
     * Create new driving score
     */
    public function create(string $vin): DrivingScore
    {
        $drivingScore = new DrivingScore();
        $drivingScore->setVin($vin);
        $drivingScore->setLastUpdate(new \DateTime());
        return $drivingScore;
    }

    /**
     * Update driving score data
     */
    public function updateScoreData(DrivingScore $drivingScore, array $overallScore = null, array $dailyScore = null): void
    {
        try {
            if ($overallScore !== null) {
                $drivingScore->setOverallScoreWithSync($overallScore);
            }

            if ($dailyScore !== null) {
                $drivingScore->setDailyScoreWithSync($dailyScore);
            }

            $drivingScore->setLastUpdate(new \DateTime());
            $this->save($drivingScore);
        } catch (\Exception $e) {
            $this->logger->error('Error updating driving score data', [
                'vin' => $drivingScore->getVin(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update driving score data', 503, $e);
        }
    }

    /**
     * Get driving score with enhanced data
     */
    public function getEnhancedScore(string $vin): ?array
    {
        try {
            $drivingScore = $this->findByVin($vin);
            if (!$drivingScore) {
                return null;
            }

            // Sync arrays from embedded documents
            $drivingScore->postLoad();

            return [
                'vin' => $drivingScore->getVin(),
                'contractNumber' => $drivingScore->getContractNumber(),
                'globalScore' => $drivingScore->getGlobalScore(),
                'accelerationScore' => $drivingScore->getAccelerationScore(),
                'brakingScore' => $drivingScore->getBrakingScore(),
                'overallScore' => $drivingScore->getOverallScoreWithSync(),
                'dailyScore' => $drivingScore->getDailyScoreWithSync(),
                'drivingQuality' => $drivingScore->getDrivingQuality(),
                'hasDetailedBreakdown' => $drivingScore->hasDetailedBreakdown(),
                'lastUpdate' => $drivingScore->getLastUpdate(),
                'isValid' => $drivingScore->getIsValid()
            ];
        } catch (\Exception $e) {
            $this->logger->error('Error getting enhanced driving score', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get enhanced driving score', 503, $e);
        }
    }

    /**
     * Update global score and component scores
     */
    public function updateGlobalScore(DrivingScore $drivingScore, int $globalScore, int $accelerationScore = null, int $brakingScore = null): void
    {
        try {
            $drivingScore->setGlobalScore($globalScore);
            
            if ($accelerationScore !== null) {
                $drivingScore->setAccelerationScore($accelerationScore);
            }
            
            if ($brakingScore !== null) {
                $drivingScore->setBrakingScore($brakingScore);
            }

            $drivingScore->setLastUpdate(new \DateTime());
            $this->save($drivingScore);
        } catch (\Exception $e) {
            $this->logger->error('Error updating global score', [
                'vin' => $drivingScore->getVin(),
                'globalScore' => $globalScore,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update global score', 503, $e);
        }
    }
}
