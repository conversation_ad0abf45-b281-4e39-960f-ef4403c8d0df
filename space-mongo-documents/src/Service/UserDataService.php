<?php

namespace Space\MongoDocuments\Service;

use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Repository\UserDataRepository;
use Psr\Log\LoggerInterface;
use RuntimeException;

class UserDataService
{
    public function __construct(
        private MongoDBService $mongoDBService,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Find user data by user ID
     */
    public function findByUserId(string $userId): ?UserData
    {
        try {
            return $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding user data by userId', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find user data', 503, $e);
        }
    }

    /**
     * Find user data by session ID (using aggregation)
     */
    public function findBySessionId(string $sessionId): array
    {
        try {
            // This would need to be implemented based on the specific aggregation logic
            // For now, returning empty array as placeholder
            return [];
        } catch (\Exception $e) {
            $this->logger->error('Error finding user data by sessionId', [
                'sessionId' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find user data by session ID', 503, $e);
        }
    }

    /**
     * Find vehicle by VIN across all users
     */
    public function findVehicleByVin(string $vin): ?array
    {
        try {
            $userData = $this->mongoDBService->findOneBy(UserData::class, [
                'vehicle' => [
                    '$elemMatch' => ['vin' => $vin]
                ]
            ]);

            if ($userData) {
                $vehicle = $userData->findVehicleByVin($vin);
                if ($vehicle) {
                    return [
                        'userData' => $userData,
                        'vehicle' => $vehicle
                    ];
                }
            }

            return null;
        } catch (\Exception $e) {
            $this->logger->error('Error finding vehicle by VIN', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find vehicle by VIN', 503, $e);
        }
    }

    /**
     * Update vehicle feature code
     */
    public function updateVehicleFeatureCode(string $userId, string $vin, array $featureCodeData): bool
    {
        try {
            $userData = $this->findByUserId($userId);
            if (!$userData) {
                return false;
            }

            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return false;
            }

            // Update feature code logic would go here
            // This is a placeholder implementation
            
            $this->mongoDBService->save($userData);
            return true;
        } catch (\Exception $e) {
            $this->logger->error('Error updating vehicle feature code', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update vehicle feature code', 503, $e);
        }
    }

    /**
     * Save user data
     */
    public function save(UserData $userData): void
    {
        try {
            $this->mongoDBService->save($userData);
        } catch (\Exception $e) {
            $this->logger->error('Error saving user data', [
                'userId' => $userData->getUserId(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save user data', 503, $e);
        }
    }

    /**
     * Create new user data
     */
    public function create(string $userId): UserData
    {
        $userData = new UserData();
        $userData->setUserId($userId);
        return $userData;
    }

    /**
     * Find or create user data
     */
    public function findOrCreate(string $userId): UserData
    {
        $userData = $this->findByUserId($userId);
        if (!$userData) {
            $userData = $this->create($userId);
        }
        return $userData;
    }

    /**
     * Get preferred dealer for user and brand
     */
    public function getPreferredDealer(string $userId, string $brand): ?array
    {
        try {
            $userData = $this->findByUserId($userId);
            if (!$userData) {
                return null;
            }

            return $userData->getPreferredDealerForBrand($brand);
        } catch (\Exception $e) {
            $this->logger->error('Error getting preferred dealer', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get preferred dealer', 503, $e);
        }
    }

    /**
     * Set preferred dealer for user and brand
     */
    public function setPreferredDealer(string $userId, string $brand, array $dealerData): void
    {
        try {
            $userData = $this->findOrCreate($userId);
            $userData->setPreferredDealerForBrand($brand, $dealerData);
            $this->save($userData);
        } catch (\Exception $e) {
            $this->logger->error('Error setting preferred dealer', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to set preferred dealer', 503, $e);
        }
    }

    /**
     * Remove preferred dealer for user and brand
     */
    public function removePreferredDealer(string $userId, string $brand): void
    {
        try {
            $userData = $this->findByUserId($userId);
            if ($userData) {
                $userData->removePreferredDealerForBrand($brand);
                $this->save($userData);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error removing preferred dealer', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to remove preferred dealer', 503, $e);
        }
    }
}
